<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متابعة الصفقات الشاملة - تداولجي</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/mobile.css" media="screen and (max-width: 768px)">






</head>
<body>

<!-- Header, Container, Sections, etc. as before -->
<div class="page-header">
    <strong>تم التصميم بواسطة: المهندس معتز [تداولجي] @TadawulGy</strong> -
    <a href="https://t.me/TadawulGY" target="_blank" rel="noopener noreferrer" class="telegram-link">قناة التليغرام</a> <br>
    نسألكم الدعاء بظهر الغيب
</div>

<div class="container">
    <h1>متابعة الصفقات الشاملة</h1>

    <section class="overall-summary-section">
        <h2>ملخص الصفقات الكلي</h2>
        <div class="summary-table-container">
            <table class="summary-table">
                <thead>
                     <tr>
                         <th>العملة</th><th>الكمية</th><th>المستثمر $</th><th>متوسط الدخول</th>
                         <th>السعر الحالي</th><th>القيمة الحالية $</th><th>الربح/الخسارة $</th><th>الربح/الخسارة %</th>
                     </tr>
                </thead>
                <tbody id="summaryTableBody">
                    <tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">أضف عملة أو قم بتحديث الأسعار لعرض الملخص...</td></tr>
                </tbody>
            </table>
        </div>
        <div class="summary-totals">
             <div> <label>إجمالي المبلغ المستثمر</label> <span id="totalInvestedSummary">0.00 $</span> </div>
             <div> <label>إجمالي الربح / الخسارة $</label> <span id="totalPnlAmountSummary">0.00 $</span> </div>
             <div> <label>إجمالي القيمة الحالية</label> <span id="totalCurrentValueSummary">0.00 $</span> </div>
             <div> <label>إجمالي الربح / الخسارة %</label> <span id="totalPnlPercentSummary">0.00 %</span> </div>
        </div>
    </section>

    <div class="controls-bar">
        <label for="coinSelector">العملة النشطة:</label>
        <select id="coinSelector" onchange="handleCoinSelectionChange()"></select>
        <label for="newCoinName">إضافة/بحث:</label>
        <input type="text" id="newCoinName" placeholder="BTCUSDT">
        <button onclick="addOrSwitchCoin()">➕ إضافة/تبديل</button>
        <button id="refreshPriceBtn" title="تحديث كل الأسعار" onclick="fetchAllPrices()">🔄تحديث الاسعار</button>
        <div id="apiStatus">اختر عملة لجلب السعر</div>
        <div id="coinStatus">العملات: 0</div>
        <button id="themeToggleBtn" title="تبديل الوضع">🌙</button>
        
<input type="file" id="importTxtFile" accept=".txt" style="display:none" onchange="handleImportTxtFile(event)">
<button onclick="document.getElementById('importTxtFile').click()">استيراد TXT</button>
<button onclick="downloadAllDataAsTxt()">حفظ كـ TXT</button>

<button id="deleteCoinBtn" onclick="deleteCurrentCoin()" disabled title="حذف العملة المحددة">🗑️ حذف</button>
    </div>

    <div class="details-content-wrapper">
        <section class="section section-initial">
            <h2>تفاصيل الدخول (<span id="currentCoinDisplay1">---</span>)</h2>
            <div class="form-grid initial-purchase-grid">
                <div class="form-group">
                    <label for="initialEntryPrice">سعر الدخول الأولي:</label>
                    <input type="number" id="initialEntryPrice" step="any" placeholder="0.00" oninput="saveAndCalculate()">
                </div>
                <div class="form-group">
                    <label for="initialAmountDollars">المبلغ بالدولار $:</label>
                    <input type="number" id="initialAmountDollars" step="any" placeholder="0.00" oninput="saveAndCalculate()">
                </div>
                 <div class="form-group" style="grid-column: span 2;">
                     <label>كمية العملة المحصلة:</label>
                     <div id="initialCoinQty" class="output-field">0.00</div>
                 </div>
                 <div class="market-price-display-group" style="grid-column: span 2;">
                     <div class="form-group">
                         <label>السعر الحالي (للعملة النشطة):</label>
                         <div id="marketPriceDisplay" class="output-field">---</div>
                     </div>
                 </div>
                 <div class="auto-refresh-group" style="grid-column: span 2;">
                     <input type="checkbox" id="autoRefreshCheckbox">
                     <label for="autoRefreshCheckbox">تحديث تلقائي للأسعار (كل 30 ثانية)</label>
                 </div>
            </div>
        </section>

        <section class="section section-repurchase">
            <h2>التعزيز DCA (<span id="currentCoinDisplay2">---</span>)</h2>
            <div style="overflow-x: auto;">
                <table class="repurchase-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>هبوط %</th>
                            <th>سعر التعزيز</th>
                            <th>المبلغ $</th>
                            <th>الكمية</th>
                            <th>ربح/خسارة الصفقة $</th>
                            <th>ربح/خسارة الصفقة %</th>
                        </tr>
                    </thead>
                    <tbody id="repurchaseRows"></tbody>
                </table>
            </div>
        </section>

        <section class="section section-current-targets">
            <h2>الوضع الحالي والأهداف (<span id="currentCoinDisplay3">---</span>)</h2>
            <div class="summary-grid">
                <div class="summary-item"> <label>إجمالي الكمية:</label> <div id="totalCoinQty" class="value">0.00</div> </div>
                <div class="summary-item"> <label>إجمالي المستثمر $:</label> <div id="totalInvestedAmount" class="value">0.00</div> </div>
                <div class="summary-item"> <label>متوسط السعر:</label> <div id="averageEntryPrice" class="value">0.00</div> </div>
                <div class="summary-item"> <label>القيمة الحالية $:</label> <div id="currentPortfolioValue" class="value">0.00</div> </div>
                <div class="summary-item"> <label>الربح/الخسارة $:</label> <div id="pnlAmount" class="value pnl-neutral">0.00</div> </div>
                <div class="summary-item"> <label>الربح/الخسارة %:</label> <div id="pnlPercent" class="value pnl-neutral">0.00%</div> </div>
            </div>

            <div id="targetsSection">
                 <h3>الأهداف ووقف الخسارة (من متوسط السعر)</h3>
                 <div class="targets-grid">
                    <!-- Target 1 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent1">هدف 1 (%)</label> <input type="number" id="tpPercent1" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 1</label> <div id="tpPrice1" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Target 2 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent2">هدف 2 (%)</label> <input type="number" id="tpPercent2" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 2</label> <div id="tpPrice2" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Target 3 -->
                    <div class="target-pair">
                        <div class="form-group"> <label for="tpPercent3">هدف 3 (%)</label> <input type="number" id="tpPercent3" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"> </div>
                        <div class="form-group"> <label>سعر خروج 3</label> <div id="tpPrice3" class="output-field">0.00</div> </div>
                    </div>
                    <!-- Stop Loss -->
                    <div class="sl-group"> <!-- SL group still spans full width of its parent grid context -->
                        <div class="form-group sl-input">
                            <label for="slPercent">وقف خسارة (%)</label>
                            <input type="number" id="slPercent" step="any" min="0" placeholder="%" oninput="saveAndCalculate()">
                        </div>
                        <div class="form-group">
                            <label>سعر وقف الخسارة</label>
                            <div id="slPrice" class="output-field sl-output">0.00</div>
                        </div>
                    </div>
                 </div>
            </div>
        </section>
    </div>
</div>

<script src="js/utils.js"></script>
<script src="js/app.js"></script>








</body>
</html>