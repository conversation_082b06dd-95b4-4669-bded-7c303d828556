# CryptoTracker Pro V2 📈

تطبيق شامل لمتابعة صفقات العملات الرقمية مع إمكانيات متقدمة لحساب الأرباح والخسائر وإدارة التعزيزات.

## ✨ المميزات

### 🎯 إدارة الصفقات
- متابعة عدد غير محدود من العملات الرقمية
- حساب متوسط سعر الدخول تلقائياً
- إدارة التعزيزات (حتى 10 تعزيزات لكل عملة)
- حساب الأرباح والخسائر لكل صفقة وتعزيز

### 📊 الملخص الشامل
- جدول ملخص لجميع العملات
- إجماليات المحفظة الاستثمارية
- حساب النسب المئوية للأرباح والخسائر
- عرض القيمة الحالية للمحفظة

### 🎯 الأهداف ووقف الخسارة
- تحديد 3 أهداف ربح لكل عملة
- حساب أسعار الأهداف تلقائياً
- تحديد نقطة وقف الخسارة
- عرض الأسعار المحسوبة بناءً على متوسط الدخول

### 🔄 تحديث الأسعار
- جلب الأسعار الحية من Binance و KuCoin
- تحديث تلقائي كل 30 ثانية
- إمكانية التحديث اليدوي
- عرض حالة جلب الأسعار

### 💾 إدارة البيانات
- حفظ تلقائي للبيانات محلياً
- تصدير البيانات كملف نصي
- استيراد البيانات من ملف نصي
- نسخ احتياطية آمنة

### 🎨 واجهة المستخدم
- تصميم متجاوب لجميع الأجهزة
- وضع ليلي ونهاري
- ألوان مميزة للأرباح والخسائر
- واجهة سهلة الاستخدام

## 🏗️ هيكل المشروع

```
CryptoTracker-Pro-V2/
├── index.html          # الصفحة الرئيسية
├── css/
│   ├── main.css        # الأنماط الرئيسية
│   └── mobile.css      # تحسينات الموبايل والتابلت
├── js/
│   ├── app.js          # التطبيق الرئيسي
│   └── utils.js        # الوظائف المساعدة
└── data/               # ملفات البيانات
    └── README.md       # دليل مجلد البيانات
```

## 🚀 كيفية الاستخدام

### 1. إضافة عملة جديدة
- أدخل رمز العملة (مثل BTCUSDT) في حقل "إضافة عملة"
- اضغط Enter أو زر "إضافة/تبديل"
- سيتم جلب السعر الحالي تلقائياً

### 2. إدخال بيانات الصفقة
- أدخل سعر الدخول الأولي
- أدخل المبلغ المستثمر بالدولار
- أضف التعزيزات إذا كانت موجودة
- حدد نسب الأهداف ووقف الخسارة

### 3. متابعة الأداء
- راقب الملخص الشامل لجميع العملات
- تابع الأرباح والخسائر الحالية
- استخدم التحديث التلقائي للأسعار

### 4. إدارة البيانات
- صدّر بياناتك بانتظام كنسخة احتياطية
- استورد البيانات عند الحاجة
- احذف العملات غير المرغوبة

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحة
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - المنطق والوظائف
- **Local Storage** - حفظ البيانات محلياً
- **Fetch API** - جلب أسعار العملات
- **Responsive Design** - تصميم متجاوب

## 📱 التوافق

- ✅ Chrome/Edge (الأحدث)
- ✅ Firefox (الأحدث)
- ✅ Safari (الأحدث)
- ✅ الهواتف الذكية والأجهزة اللوحية
- ✅ جميع أحجام الشاشات

## 🔧 التثبيت والتشغيل

1. حمّل جميع الملفات
2. افتح `index.html` في المتصفح
3. ابدأ بإضافة عملاتك الرقمية
4. استمتع بمتابعة صفقاتك!

## 📝 ملاحظات مهمة

- البيانات محفوظة محلياً في متصفحك
- يُنصح بتصدير نسخة احتياطية بانتظام
- الأسعار مجلبة من مصادر خارجية وقد تتأخر قليلاً
- التطبيق يعمل بدون إنترنت للبيانات المحفوظة

## 🤝 المساهمة

نرحب بالمساهمات والاقتراحات لتحسين التطبيق!

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم تطوير هذا التطبيق لمساعدة المتداولين في إدارة صفقاتهم بكفاءة وسهولة** 🚀
