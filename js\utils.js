// ===== الوظائف المساعدة =====

// --- تنسيق الأرقام ---
function formatNumber(num, decimalPlaces = 8) {
    const number = Number(num);
    if (isNaN(number) || !isFinite(number)) return (0).toFixed(decimalPlaces);
    return number.toFixed(decimalPlaces);
}

// --- تخمين عدد الخانات العشرية ---
function guessDecimalPlaces(price) {
     const num = Number(price); if (isNaN(num) || num === 0) return 2;
    if (num >= 1000) return 2; if (num >= 10) return 4; if (num >= 0.1) return 5;
    if (num >= 0.001) return 6; if (num >= 0.0001) return 7; return 8;
}

// --- Reset Totals Display ---
function resetTotals() {
    totalInvestedSummaryEl.textContent = `0.00 $`; totalPnlAmountSummaryEl.textContent = `0.00 $`;
    totalCurrentValueSummaryEl.textContent = `0.00 $`; totalPnlPercentSummaryEl.textContent = `0.00 %`;
    totalPnlAmountSummaryEl.className = ''; totalPnlPercentSummaryEl.className = ''; totalCurrentValueSummaryEl.className = '';
}

// --- دوال التحديث التلقائي ---
function startAutoRefresh() {
    if (autoRefreshIntervalId) clearInterval(autoRefreshIntervalId);
    const trackedSymbols = Object.keys(allCoinData);
    if (trackedSymbols.length > 0 && autoRefreshCheckbox.checked) {
        if (!apiStatusDiv.textContent.includes('(تلقائي مفعل)')) apiStatusDiv.textContent += ' (تلقائي مفعل)';
        autoRefreshIntervalId = setInterval(() => fetchAllPrices(true), AUTO_REFRESH_INTERVAL);
    } else {
         autoRefreshCheckbox.checked = false;
         if (trackedSymbols.length === 0) {
              apiStatusDiv.textContent = 'أضف عملة للتحديث التلقائي.'; apiStatusDiv.style.color = 'var(--text-muted)';
          }
    }
}

function stopAutoRefresh() {
    if (autoRefreshIntervalId) {
        clearInterval(autoRefreshIntervalId); autoRefreshIntervalId = null;
        let currentStatus = apiStatusDiv.textContent;
        currentStatus = currentStatus.replace(/\(تلقائي مفعل\)|\(تلقائي متوقف\)/g, '').trim();
         if (currentStatus.startsWith('تلقائي:')) {
             const timePartIndex = currentStatus.indexOf('(');
             currentStatus = timePartIndex > -1 ? currentStatus.substring(currentStatus.indexOf(':') + 1, timePartIndex).trim() : currentStatus.substring(currentStatus.indexOf(':') + 1).trim();
         }
        apiStatusDiv.textContent = currentStatus + ' (تلقائي متوقف)';
    }
}

function handleAutoRefreshToggle() {
    if (autoRefreshCheckbox.checked) {
        if (Object.keys(allCoinData).length > 0) { fetchAllPrices(); startAutoRefresh(); }
        else { autoRefreshCheckbox.checked = false; alert("يرجى إضافة عملة أولاً لتفعيل التحديث التلقائي."); }
    } else { stopAutoRefresh(); }
}

// --- التعامل مع اختيار عملة من القائمة ---
function handleCoinSelectionChange() {
    const selectedSymbol = coinSelector.value;
    if (selectedSymbol && allCoinData[selectedSymbol]) {
        activeCoinSymbol = selectedSymbol; newCoinNameInput.value = '';
        displayCoinData(selectedSymbol); saveAllDataToLocalStorage();
    } else if (!selectedSymbol) {
         activeCoinSymbol = null; clearUIFields();
         saveAllDataToLocalStorage(); updateCoinStatus();
    } else {
         console.error(`Selected symbol ${selectedSymbol} not in data.`);
         activeCoinSymbol = null; clearUIFields(); saveAllDataToLocalStorage(); updateCoinStatus();
    }
}

// --- إضافة عملة جديدة أو التبديل ---
function addOrSwitchCoin() {
    const symbol = newCoinNameInput.value.trim().toUpperCase();
    if (!symbol) { alert("يرجى إدخال رمز العملة (مثل BTCUSDT)."); return; }
    if (!/^[A-Z0-9]{3,15}$/.test(symbol)) {
        alert(`رمز العملة "${symbol}" غير صالح.`); return;
    }
    if (allCoinData[symbol]) {
        coinSelector.value = symbol; activeCoinSymbol = symbol;
        displayCoinData(symbol); saveAllDataToLocalStorage(); newCoinNameInput.value = '';
         apiStatusDiv.textContent = `تم التبديل إلى ${symbol}.`; apiStatusDiv.style.color = 'var(--text-muted)';
    } else {
        allCoinData[symbol] = getDefaultCoinDataStructure(); currentMarketPrices[symbol] = null;
        activeCoinSymbol = symbol; updateCoinSelector(); newCoinNameInput.value = '';
        saveAllDataToLocalStorage(); updateCoinStatus(); updateSummaryTable();
        alert(`تمت إضافة ${symbol}. قم بتحديث الأسعار وأدخل التفاصيل.`);
         apiStatusDiv.textContent = `تمت إضافة ${symbol}.`; apiStatusDiv.style.color = 'var(--positive-color)';
         fetchSinglePrice(symbol).then(result => {
              if (result.price !== null) {
                  currentMarketPrices[symbol] = result.price;
                  if(activeCoinSymbol === symbol) displayCoinData(symbol); // Ensure displayCoinData is called for the newly added active coin
                  updateSummaryTable();
                  apiStatusDiv.textContent = `تم جلب سعر ${symbol}.`; apiStatusDiv.style.color = 'var(--positive-color)';
              } else {
                   apiStatusDiv.textContent = `فشل جلب سعر ${symbol} (${result.error}).`; apiStatusDiv.style.color = 'var(--negative-color)';
              }
         });
    }
}

// --- حذف العملة النشطة حاليًا ---
function deleteCurrentCoin() {
    if (!activeCoinSymbol) { alert("لا توجد عملة محددة لحذفها."); return; }
    const symbolToDelete = activeCoinSymbol;
    if (confirm(`هل أنت متأكد أنك تريد حذف بيانات العملة ${symbolToDelete}؟`)) {
        delete allCoinData[symbolToDelete]; delete currentMarketPrices[symbolToDelete];
        activeCoinSymbol = null; updateCoinSelector(); saveAllDataToLocalStorage();
        if (!coinSelector.value) clearUIFields();
        updateSummaryTable(); updateCoinStatus();
        alert(`تم حذف العملة ${symbolToDelete}.`);
         apiStatusDiv.textContent = `تم حذف ${symbolToDelete}.`; apiStatusDiv.style.color = 'var(--text-muted)';
    }
}

// --- وظائف الاستيراد والتصدير ---
function downloadAllDataAsTxt() {
    if (!allCoinData || Object.keys(allCoinData).length === 0) {
        alert("لا توجد بيانات لحفظها.");
        return;
    }

    let content = "";
    for (const [symbol, data] of Object.entries(allCoinData)) {
        content += `رمز: ${symbol}\n`;
        content += `سعر الدخول: ${data.initialEntryPrice || 0}\n`;
        content += `المبلغ: ${data.initialAmountDollars || 0}\n`;

        data.repurchases.forEach((rp, i) => {
            if (rp.price && rp.amount) {
                content += `تعزيز ${i + 1}: ${rp.price}, ${rp.amount}\n`;
            }
        });

        content += `هدف1: ${data.targets.tp1 || 0}\n`;
        content += `هدف2: ${data.targets.tp2 || 0}\n`;
        content += `هدف3: ${data.targets.tp3 || 0}\n`;
        content += `وقف: ${data.targets.sl || 0}\n`;
        content += `\n`;
    }

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const link = document.createElement('a');
    link.download = 'بيانات_العملات.txt';
    link.href = URL.createObjectURL(blob);
    link.click();
}

function handleImportTxtFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (e) {
        const lines = e.target.result.split('\n');
        let symbol = null;
        let coinData = getDefaultCoinDataStructure();
        let repIndex = 0;

        lines.forEach(line => {
            line = line.trim();
            if (!line) return;

            if (line.startsWith('رمز:')) {
                if (symbol && coinData) {
                    allCoinData[symbol] = coinData;
                }
                symbol = line.split('رمز:')[1].trim();
                coinData = getDefaultCoinDataStructure();
                repIndex = 0;
            } else if (line.startsWith('سعر الدخول:')) {
                coinData.initialEntryPrice = line.split(':')[1].trim();
            } else if (line.startsWith('المبلغ:')) {
                coinData.initialAmountDollars = line.split(':')[1].trim();
            } else if (line.startsWith('تعزيز')) {
                const parts = line.split(':')[1].split(',');
                if (parts.length === 2 && repIndex < coinData.repurchases.length) {
                    coinData.repurchases[repIndex].price = parts[0].trim();
                    coinData.repurchases[repIndex].amount = parts[1].trim();
                    repIndex++;
                }
            } else if (line.startsWith('هدف1:')) {
                coinData.targets.tp1 = line.split(':')[1].trim();
            } else if (line.startsWith('هدف2:')) {
                coinData.targets.tp2 = line.split(':')[1].trim();
            } else if (line.startsWith('هدف3:')) {
                coinData.targets.tp3 = line.split(':')[1].trim();
            } else if (line.startsWith('وقف:')) {
                coinData.targets.sl = line.split(':')[1].trim();
            }
        });

        if (symbol && coinData) {
            allCoinData[symbol] = coinData;
        }

        saveAllDataToLocalStorage();
        updateCoinSelector();
        alert("تم استيراد البيانات بنجاح. اختر عملة لعرض تفاصيلها.");
    };

    reader.readAsText(file);
}
