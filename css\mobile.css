/* ===== أنماط الهاتف المحمول والتابلت ===== */

/* التابلت - 768px وأقل */
@media (max-width: 768px) {
    .container { 
        padding: 0 15px; 
    }
    
    h1 { 
        font-size: 1.6em; 
        margin-bottom: 20px;
    } 
    
    h2 { 
        font-size: 1.3em; 
        margin-bottom: 20px;
    }
    
    .overall-summary-section { 
        position: static; 
        margin-bottom: 20px; 
    }
    
    .summary-totals { 
        grid-template-columns: 1fr 1fr; 
    }
    
    .controls-bar { 
        flex-direction: column; 
        align-items: stretch; 
        gap: 10px; 
    }
    
    .controls-bar button, .controls-bar select, .controls-bar input { 
        width: 100%; 
    }
    
    .controls-bar button#deleteCoinBtn { 
        margin-top: 5px; 
    }
    
    #apiStatus { 
        text-align: center; 
        margin: 8px 0; 
    }
    
    .details-content-wrapper { 
        gap: 20px; 
        grid-template-columns: 1fr !important; 
    }
    
    .section { 
        padding: 20px; 
    } 
    
    .form-grid { 
        grid-template-columns: 1fr; 
        gap: 15px;
    }
    
    .market-price-display-group { 
        grid-column: auto; 
    } 
    
    .auto-refresh-group { 
        grid-column: auto; 
    }
    
    /* تخطيط الأهداف للتابلت */
    .targets-grid { 
        grid-template-columns: 1fr; 
    }
    
    .target-pair { 
        grid-template-columns: 1fr 1fr; /* الحفاظ على الأزواج جنباً إلى جنب إذا كان هناك مساحة */
    }
    
    .sl-group { 
        grid-template-columns: 1fr; 
        gap: 12px;
    }
}

/* الهاتف المحمول - 480px وأقل */
@media (max-width: 480px) {
    body { 
        font-size: 13px; 
    } 
    
    .container { 
        padding: 0 10px; 
    }
    
    h1 { 
        font-size: 1.4em; 
    } 
    
    h2 { 
        font-size: 1.2em; 
    }
    
    .summary-table { 
        font-size: 0.75em; 
    }
    
    .summary-table th, .summary-table td { 
        padding: 6px 5px; 
        white-space: normal; 
    }
    
    .summary-totals { 
        grid-template-columns: 1fr; 
        gap: 10px; 
    }
    
    .repurchase-table { 
        font-size: 0.8em; 
    }
    
    .repurchase-table th, .repurchase-table td { 
        padding: 6px 4px; 
        white-space: normal;
    }
    
    .summary-grid { 
        grid-template-columns: 1fr; 
    }
    
    /* تخطيط الأهداف للهاتف المحمول */
    .target-pair { 
        grid-template-columns: 1fr; /* تكديس إدخال النسبة المئوية وإخراج السعر في الشاشات الصغيرة جداً */
    }
    
    /* تحسينات إضافية للهاتف المحمول */
    .page-header {
        padding: 10px 15px;
        font-size: 0.9em;
    }
    
    .controls-bar {
        padding: 12px 15px;
    }
    
    .controls-bar label {
        font-size: 0.9em;
    }
    
    .controls-bar button {
        padding: 8px 15px;
        font-size: 0.9em;
    }
    
    .section {
        padding: 15px;
    }
    
    .form-grid {
        gap: 12px;
    }
    
    .summary-table-container {
        max-height: 200px;
    }
    
    .repurchase-table input[type="number"] {
        width: 95%;
        padding: 4px 6px;
        font-size: 0.9em;
    }
    
    .targets-grid input[type="number"] {
        padding: 6px 8px;
        font-size: 0.9em;
    }
    
    .targets-grid .output-field {
        padding: 6px 8px;
        min-height: 32px;
        font-size: 0.9em;
    }
    
    .auto-refresh-group {
        padding: 6px 10px;
        font-size: 0.9em;
    }
    
    .auto-refresh-group input[type="checkbox"] {
        width: 14px;
        height: 14px;
    }
    
    #themeToggleBtn {
        font-size: 1.1em;
        padding: 4px 8px;
    }
    
    .summary-item {
        padding: 12px;
    }
    
    .summary-item label {
        font-size: 0.8em;
    }
    
    .summary-item .value {
        font-size: 1.1em;
    }
    
    .down-percent {
        min-width: 50px;
        padding: 2px 4px;
        font-size: 0.8em;
    }
}

/* الشاشات الصغيرة جداً - 360px وأقل */
@media (max-width: 360px) {
    body {
        font-size: 12px;
    }
    
    .container {
        padding: 0 8px;
    }
    
    h1 {
        font-size: 1.3em;
        margin-bottom: 15px;
    }
    
    h2 {
        font-size: 1.1em;
        margin-bottom: 15px;
    }
    
    .page-header {
        padding: 8px 10px;
        font-size: 0.85em;
    }
    
    .controls-bar {
        padding: 10px 12px;
        gap: 8px;
    }
    
    .section {
        padding: 12px;
    }
    
    .summary-table {
        font-size: 0.7em;
    }
    
    .summary-table th, .summary-table td {
        padding: 4px 3px;
    }
    
    .repurchase-table {
        font-size: 0.75em;
    }
    
    .repurchase-table th, .repurchase-table td {
        padding: 4px 2px;
    }
    
    .summary-totals {
        padding: 10px 15px;
        gap: 8px;
    }
    
    .summary-totals label {
        font-size: 0.8em;
    }
    
    .summary-totals span {
        font-size: 1.1em;
    }
    
    .summary-item {
        padding: 10px;
    }
    
    .summary-item label {
        font-size: 0.75em;
    }
    
    .summary-item .value {
        font-size: 1em;
    }
    
    .targets-grid input[type="number"] {
        padding: 5px 6px;
        font-size: 0.85em;
    }
    
    .targets-grid .output-field {
        padding: 5px 6px;
        min-height: 28px;
        font-size: 0.85em;
    }
    
    .down-percent {
        min-width: 45px;
        padding: 1px 3px;
        font-size: 0.75em;
    }
}

/* تحسينات للمناظر الأفقية على الهواتف */
@media (max-height: 500px) and (orientation: landscape) {
    .overall-summary-section {
        position: static;
    }
    
    .summary-table-container {
        max-height: 150px;
    }
    
    .section {
        padding: 15px;
    }
    
    .form-grid {
        gap: 10px;
    }
    
    .targets-grid {
        gap: 12px;
    }
    
    .sl-group {
        gap: 10px;
    }
}
