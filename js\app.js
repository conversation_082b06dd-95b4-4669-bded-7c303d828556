// ===== متابعة الصفقات الشاملة - الملف الرئيسي =====

// --- المتغيرات العامة ---
const repurchaseTableBody = document.getElementById('repurchaseRows');
const marketPriceDisplay = document.getElementById('marketPriceDisplay');
const apiStatusDiv = document.getElementById('apiStatus');
const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
const coinSelector = document.getElementById('coinSelector');
const newCoinNameInput = document.getElementById('newCoinName');
const coinStatusDiv = document.getElementById('coinStatus');
const summaryTableBody = document.getElementById('summaryTableBody');
const totalInvestedSummaryEl = document.getElementById('totalInvestedSummary');
const totalPnlAmountSummaryEl = document.getElementById('totalPnlAmountSummary');
const totalCurrentValueSummaryEl = document.getElementById('totalCurrentValueSummary');
const totalPnlPercentSummaryEl = document.getElementById('totalPnlPercentSummary');
const themeToggleBtn = document.getElementById('themeToggleBtn');
const currentCoinDisplayElements = [
    document.getElementById('currentCoinDisplay1'),
    document.getElementById('currentCoinDisplay2'),
    document.getElementById('currentCoinDisplay3')
];

const maxRepurchaseEntries = 10;
let fetchTimeout, autoRefreshIntervalId = null;
const AUTO_REFRESH_INTERVAL = 30000;
const LS_KEY_DATA = 'cryptoTrackerUniversal_v9_data'; // Incremented version
const LS_KEY_THEME = 'cryptoTrackerUniversal_v9_theme';
let allCoinData = {};
let currentMarketPrices = {};
let activeCoinSymbol = null;

// --- تهيئة هيكل بيانات العملة الافتراضي ---
function getDefaultCoinDataStructure() {
    const repurchases = Array.from({ length: maxRepurchaseEntries }, () => ({ price: '', amount: '' }));
    return {
        initialEntryPrice: '', initialAmountDollars: '',
        repurchases: repurchases,
        targets: { tp1: '', tp2: '', tp3: '', sl: '' }
    };
 }

// --- حفظ بيانات العملات ---
function saveAllDataToLocalStorage() {
    if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
        updateActiveCoinDataInMemory();
    }
    const dataToSave = { coins: allCoinData, active: activeCoinSymbol };
    try {
        localStorage.setItem(LS_KEY_DATA, JSON.stringify(dataToSave));
    } catch (error) {
        console.error("Error saving coin data:", error);
        apiStatusDiv.textContent = "خطأ في حفظ البيانات!";
        apiStatusDiv.style.color = 'var(--negative-color)';
    }
 }

// --- تحميل بيانات العملات ---
function loadAllDataFromLocalStorage() {
    const savedData = localStorage.getItem(LS_KEY_DATA);
    if (savedData) {
        try {
            const parsedData = JSON.parse(savedData);
            if (parsedData && typeof parsedData.coins === 'object' && parsedData.coins !== null) {
                allCoinData = parsedData.coins;
                activeCoinSymbol = parsedData.active || null;
                Object.keys(allCoinData).forEach(symbol => {
                    if (!allCoinData[symbol]) {
                       allCoinData[symbol] = getDefaultCoinDataStructure();
                    }
                    if (!allCoinData[symbol].repurchases || allCoinData[symbol].repurchases.length !== maxRepurchaseEntries) {
                        const existingRepurchases = allCoinData[symbol].repurchases || [];
                        allCoinData[symbol].repurchases = Array.from({ length: maxRepurchaseEntries }, (_, i) =>
                            existingRepurchases[i] || { price: '', amount: '' }
                        );
                    }
                    if (!allCoinData[symbol].targets) {
                         allCoinData[symbol].targets = { tp1: '', tp2: '', tp3: '', sl: '' };
                    }
                    currentMarketPrices[symbol] = null;
                });
            } else {
                allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
            }
            return true;
        } catch (error) {
            console.error("Error loading or parsing coin data:", error);
            allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
            localStorage.removeItem(LS_KEY_DATA);
            return false;
        }
    }
    allCoinData = {}; activeCoinSymbol = null; currentMarketPrices = {};
    return false;
 }

 // --- Theme Management ---
function applyTheme(theme) {
    if (theme === 'dark') {
        document.body.classList.add('dark-mode');
        themeToggleBtn.textContent = '☀️';
        themeToggleBtn.title = 'الوضع النهاري';
    } else {
        document.body.classList.remove('dark-mode');
        themeToggleBtn.textContent = '🌙';
        themeToggleBtn.title = 'الوضع الليلي';
    }
}

function toggleTheme() {
    const currentTheme = document.body.classList.contains('dark-mode') ? 'light' : 'dark';
    applyTheme(currentTheme);
    localStorage.setItem(LS_KEY_THEME, currentTheme);
}

function loadThemePreference() {
    const preferredTheme = localStorage.getItem(LS_KEY_THEME) || 'light'; // Default to light
    applyTheme(preferredTheme);
}

// --- تحديث القائمة المنسدلة للعملات ---
function updateCoinSelector() {
    const previouslySelected = coinSelector.value;
    coinSelector.innerHTML = '<option value="">-- اختر عملة --</option>';
    const coinSymbols = Object.keys(allCoinData).sort();
    coinSymbols.forEach(symbol => {
        const option = document.createElement('option');
        option.value = symbol; option.textContent = symbol;
        coinSelector.appendChild(option);
    });
    if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
        coinSelector.value = activeCoinSymbol;
    } else if (previouslySelected && allCoinData[previouslySelected]) {
        coinSelector.value = previouslySelected; activeCoinSymbol = previouslySelected;
    } else if (coinSymbols.length > 0) {
        coinSelector.value = coinSymbols[0]; activeCoinSymbol = coinSymbols[0];
    } else {
        activeCoinSymbol = null; clearUIFields();
    }
    if (coinSelector.value) {
        displayCoinData(coinSelector.value);
    } else {
         updateCurrentCoinDisplay("لا عملة محددة");
    }
    updateCoinStatus();
}

// --- عرض بيانات عملة محددة في الواجهة ---
function displayCoinData(symbol) {
    activeCoinSymbol = symbol;
    const data = allCoinData[symbol];
    if (!data) {
        console.warn(`No data for symbol: ${symbol}. Clearing UI.`);
        clearUIFields(); updateCurrentCoinDisplay(symbol || "خطأ"); calculateActiveCoinDetails(); return;
    }
    updateCurrentCoinDisplay(symbol);
    document.getElementById('initialEntryPrice').value = data.initialEntryPrice || '';
    document.getElementById('initialAmountDollars').value = data.initialAmountDollars || '';
    if (data.repurchases && data.repurchases.length === maxRepurchaseEntries) {
        for (let i = 0; i < maxRepurchaseEntries; i++) {
            const priceInput = document.getElementById(`repurchasePrice${i + 1}`);
            const amountInput = document.getElementById(`repurchaseAmount${i + 1}`);
            if (priceInput) priceInput.value = data.repurchases[i]?.price || '';
            if (amountInput) amountInput.value = data.repurchases[i]?.amount || '';
        }
    } else {
         for (let i = 1; i <= maxRepurchaseEntries; i++) {
             const pIn = document.getElementById(`repurchasePrice${i}`); if(pIn) pIn.value = '';
             const aIn = document.getElementById(`repurchaseAmount${i}`); if(aIn) aIn.value = '';
         }
     }
    if (data.targets) {
        document.getElementById('tpPercent1').value = data.targets.tp1 || '';
        document.getElementById('tpPercent2').value = data.targets.tp2 || '';
        document.getElementById('tpPercent3').value = data.targets.tp3 || '';
        document.getElementById('slPercent').value = data.targets.sl || '';
    } else {
         document.getElementById('tpPercent1').value = ''; document.getElementById('tpPercent2').value = '';
         document.getElementById('tpPercent3').value = ''; document.getElementById('slPercent').value = '';
    }
    const activeCoinPrice = currentMarketPrices[symbol];
    if (activeCoinPrice !== null && activeCoinPrice !== undefined && !isNaN(activeCoinPrice)) {
        marketPriceDisplay.textContent = formatNumber(activeCoinPrice, guessDecimalPlaces(activeCoinPrice));
        marketPriceDisplay.classList.remove('error');
    } else {
        marketPriceDisplay.textContent = '---'; marketPriceDisplay.classList.add('error');
    }
    calculateActiveCoinDetails(); updateCoinStatus();
}

// --- مسح جميع حقول الإدخال والإخراج في الواجهة ---
function clearUIFields() {
    document.getElementById('initialEntryPrice').value = '';
    document.getElementById('initialAmountDollars').value = '';
    document.getElementById('tpPercent1').value = ''; document.getElementById('tpPercent2').value = '';
    document.getElementById('tpPercent3').value = ''; document.getElementById('slPercent').value = '';
    for (let i = 1; i <= maxRepurchaseEntries; i++) {
        const pIn = document.getElementById(`repurchasePrice${i}`); if(pIn) pIn.value = '';
        const aIn = document.getElementById(`repurchaseAmount${i}`); if(aIn) aIn.value = '';
    }
    marketPriceDisplay.textContent = '---'; marketPriceDisplay.classList.remove('error');
    apiStatusDiv.textContent = 'اختر عملة أو أضف واحدة جديدة'; apiStatusDiv.style.color = 'var(--text-muted)';
    updateCurrentCoinDisplay("لا عملة محددة"); calculateActiveCoinDetails();
}

// --- تحديث اسم العملة المعروض في العناوين ---
function updateCurrentCoinDisplay(symbol) {
    const displayText = symbol || "---";
    currentCoinDisplayElements.forEach(el => { if (el) el.textContent = displayText; });
}

 // --- تحديث رسالة حالة العملة (عدد العملات) ---
 function updateCoinStatus() {
     const count = Object.keys(allCoinData).length;
     coinStatusDiv.textContent = `العملات: ${count}`;
     document.getElementById('deleteCoinBtn').disabled = !activeCoinSymbol;
 }

// --- قراءة البيانات الحالية من الواجهة وتحديثها في كائن allCoinData (في الذاكرة) ---
function updateActiveCoinDataInMemory() {
    if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;
    const currentData = allCoinData[activeCoinSymbol];
    currentData.initialEntryPrice = document.getElementById('initialEntryPrice').value;
    currentData.initialAmountDollars = document.getElementById('initialAmountDollars').value;
    currentData.repurchases = [];
    for (let i = 1; i <= maxRepurchaseEntries; i++) {
        currentData.repurchases.push({
            price: document.getElementById(`repurchasePrice${i}`)?.value || '',
            amount: document.getElementById(`repurchaseAmount${i}`)?.value || ''
        });
    }
    currentData.targets = {
        tp1: document.getElementById('tpPercent1').value, tp2: document.getElementById('tpPercent2').value,
        tp3: document.getElementById('tpPercent3').value, sl: document.getElementById('slPercent').value
    };
}

// --- دالة الحفظ والحساب (للعملة النشطة + تحديث الملخص) ---
function saveAndCalculate() {
    calculateActiveCoinDetails();
    if (activeCoinSymbol) {
         updateActiveCoinDataInMemory(); saveAllDataToLocalStorage();
    }
    updateSummaryTable();
}

// --- إنشاء صفوف التعزيز ---
function createRepurchaseRows() {
    repurchaseTableBody.innerHTML = '';
    for (let i = 1; i <= maxRepurchaseEntries; i++) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${i}</td>
            <td><span class="down-percent" id="downPercent${i}">-%</span></td>
            <td><input type="number" id="repurchasePrice${i}" step="any" placeholder="السعر" oninput="saveAndCalculate()"></td>
            <td><input type="number" id="repurchaseAmount${i}" step="any" placeholder="$" oninput="saveAndCalculate()"></td>
            <td><div class="output-field" id="repurchaseQty${i}">0.00</div></td>
            <td><div class="output-field repurchase-pnl" id="repurchasePnl${i}">0.00</div></td>
            <td><div class="output-field repurchase-pnl-percent" id="repurchasePnlPercent${i}">0.00%</div></td>
        `;
        repurchaseTableBody.appendChild(row);
    }
}

// --- جلب سعر **واحد** لعملة محددة ---
async function fetchSinglePrice(symbol) {
    if (!symbol || typeof symbol !== 'string' || symbol.length < 4) {
         return { symbol: symbol, price: null, source: null, error: 'رمز غير صالح' };
    }
    const binanceApiUrl = `https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`;
    const kucoinSymbol = symbol.endsWith('USDT') ? `${symbol.slice(0, -4)}-USDT` : symbol;
    const kucoinApiUrl = `https://api.kucoin.com/api/v1/market/orderbook/level1?symbol=${kucoinSymbol}`;
    let price = null; let source = ''; let error = null;
    try {
        const response = await fetch(binanceApiUrl);
        if (response.ok) {
             const data = await response.json();
             if (data && data.price && !isNaN(parseFloat(data.price))) {
                 price = parseFloat(data.price); source = 'Binance';
             }
         }
    } catch (e) { console.warn(`Binance fetch failed for ${symbol}:`, e.message); }
    if (price === null) {
        try {
            const kucoinResponse = await fetch(kucoinApiUrl);
             if (kucoinResponse.ok) {
                const kucoinData = await kucoinResponse.json();
                if (kucoinData.code === '200000' && kucoinData.data && kucoinData.data.price && !isNaN(parseFloat(kucoinData.data.price))) {
                    price = parseFloat(kucoinData.data.price); source = 'KuCoin';
                }
             }
        } catch (e) { console.warn(`KuCoin fetch failed for ${kucoinSymbol}:`, e.message); }
    }
    if (price !== null) {
        return { symbol: symbol, price: price, source: source, error: null };
    } else {
         error = 'فشل جلب السعر';
         console.error(`Failed to fetch price for ${symbol} from both Binance and KuCoin.`);
        return { symbol: symbol, price: null, source: null, error: error };
    }
 }

// --- جلب أسعار **كل** العملات المراقبة ---
async function fetchAllPrices(isAutoRefresh = false) {
    const trackedSymbols = Object.keys(allCoinData);
    if (trackedSymbols.length === 0) {
        if (!isAutoRefresh) {
            apiStatusDiv.textContent = 'لا توجد عملات للمراقبة.'; apiStatusDiv.style.color = 'var(--text-muted)';
            summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">أضف عملة للبدء.</td></tr>';
            resetTotals();
        } return;
    }
    if (!isAutoRefresh) {
        apiStatusDiv.textContent = `جاري جلب أسعار ${trackedSymbols.length} عملة...`; apiStatusDiv.style.color = 'var(--primary-color)';
        summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">جاري تحديث الأسعار...</td></tr>';
    }
    const pricePromises = trackedSymbols.map(symbol => fetchSinglePrice(symbol));
    try {
        const results = await Promise.allSettled(pricePromises);
        let successCount = 0; let failCount = 0; const fetchTimestamp = new Date();
        results.forEach(result => {
             if (result.status === 'fulfilled') {
                const { symbol, price, error } = result.value;
                if (error === null && price !== null) {
                    currentMarketPrices[symbol] = price; successCount++;
                } else {
                    if (currentMarketPrices[symbol] === undefined) { currentMarketPrices[symbol] = null; }
                    failCount++; console.error(`Price fetch logic error for ${symbol}: ${error || 'No price'}`);
                }
             } else {
                 failCount++; console.error(`Promise rejected for symbol fetch:`, result.reason);
                  const failedSymbol = trackedSymbols.find(s => result.reason?.message?.includes(s));
                  if (failedSymbol && currentMarketPrices[failedSymbol] === undefined) {
                      currentMarketPrices[failedSymbol] = null;
                  }
             }
        });
        if (activeCoinSymbol) {
             const price = currentMarketPrices[activeCoinSymbol];
             if (price !== null && price !== undefined && !isNaN(price)) {
                 marketPriceDisplay.textContent = formatNumber(price, guessDecimalPlaces(price)); marketPriceDisplay.classList.remove('error');
             } else {
                 marketPriceDisplay.textContent = "فشل الجلب"; marketPriceDisplay.classList.add('error');
             }
        }
        calculateActiveCoinDetails(); updateSummaryTable();
        const statusMsg = `${successCount} ناجح, ${failCount} فشل (${fetchTimestamp.toLocaleTimeString('ar-EG')})`;
        apiStatusDiv.textContent = isAutoRefresh ? `تلقائي: ${statusMsg}` : `اكتمل: ${statusMsg}`;
        apiStatusDiv.style.color = failCount > 0 ? 'var(--negative-color)' : 'var(--positive-color)';
        if (autoRefreshCheckbox.checked && !autoRefreshIntervalId) { startAutoRefresh(); }
    } catch (error) {
        console.error("Unexpected error during fetchAllPrices:", error);
        if (!isAutoRefresh) {
            apiStatusDiv.textContent = `خطأ عام أثناء تحديث الأسعار.`; apiStatusDiv.style.color = 'var(--negative-color)';
        }
        stopAutoRefresh(); updateSummaryTable();
    }
}

// --- حساب بيانات الملخص لكل العملات ---
function calculateSummaryData() {
    const summaryData = []; const coinSymbols = Object.keys(allCoinData);
    let grandTotalInvested = 0; let grandTotalPnlAmount = 0;
    coinSymbols.forEach(symbol => {
        const data = allCoinData[symbol]; if (!data) return;
        const marketPrice = currentMarketPrices[symbol];
        const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
        const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;
        let totalCoinQty = 0; let totalInvestedAmount = 0; let errorMsg = null;
        if (initialEntryPrice > 0 && initialAmountDollars > 0) {
            totalCoinQty = initialAmountDollars / initialEntryPrice; totalInvestedAmount = initialAmountDollars;
        } else if (initialAmountDollars > 0 && initialEntryPrice <= 0) { errorMsg = "سعر الدخول الأولي مفقود"; }
        if (data.repurchases) {
            data.repurchases.forEach(rp => {
                const rpPrice = parseFloat(rp.price) || 0; const rpAmount = parseFloat(rp.amount) || 0;
                if (rpPrice > 0 && rpAmount > 0) {
                    totalCoinQty += rpAmount / rpPrice; totalInvestedAmount += rpAmount;
                } else if (rpAmount > 0 && rpPrice <=0) { if (!errorMsg) errorMsg = "سعر تعزيز مفقود"; }
            });
        }
        const averageEntryPrice = totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
        let currentPortfolioValue = 0; let pnlAmount = 0; let pnlPercent = 0;
        if (marketPrice === null || marketPrice === undefined) { if (!errorMsg) errorMsg = "لم يتم جلب السعر"; }
        else if (totalInvestedAmount <= 0 && totalCoinQty <= 0) { pnlAmount = 0; pnlPercent = 0; currentPortfolioValue = 0; }
        else if (errorMsg) { pnlAmount = NaN; pnlPercent = NaN; currentPortfolioValue = NaN; }
        else {
            currentPortfolioValue = totalCoinQty * marketPrice;
            pnlAmount = currentPortfolioValue - totalInvestedAmount;
            pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;
        }
        if (!isNaN(pnlAmount) && totalInvestedAmount >= 0) {
            grandTotalInvested += totalInvestedAmount; grandTotalPnlAmount += pnlAmount;
        }
        summaryData.push({
            symbol, totalCoinQty, totalInvestedAmount, averageEntryPrice, marketPrice,
            currentPortfolioValue, pnlAmount, pnlPercent, error: errorMsg
        });
    });
    summaryData.sort((a, b) => a.symbol.localeCompare(b.symbol));
    const grandTotalCurrentValue = grandTotalInvested + grandTotalPnlAmount;
    const grandTotalPnlPercent = grandTotalInvested > 0 ? (grandTotalPnlAmount / grandTotalInvested) * 100 : 0;
    return {
        summaryRows: summaryData,
        totals: { invested: grandTotalInvested, pnlAmount: grandTotalPnlAmount, currentValue: grandTotalCurrentValue, pnlPercent: grandTotalPnlPercent }
    };
}

// --- تحديث جدول الملخص + الإجماليات ---
function updateSummaryTable() {
    const { summaryRows, totals } = calculateSummaryData();
    summaryTableBody.innerHTML = '';
    if (summaryRows.length === 0) {
        summaryTableBody.innerHTML = '<tr><td colspan="8" style="text-align:center; padding: 30px; font-weight: normal; color: var(--text-muted);">لا توجد عملات مضافة حالياً.</td></tr>';
        resetTotals(); return;
    }
    summaryRows.forEach(item => {
        const row = document.createElement('tr');
        const pnlAmountValid = !isNaN(item.pnlAmount); const pnlPercentValid = !isNaN(item.pnlPercent);
        const marketPriceValid = item.marketPrice !== null && !isNaN(item.marketPrice);
        const avgPriceValid = !isNaN(item.averageEntryPrice) && item.averageEntryPrice > 0;
        const portfolioValueValid = !isNaN(item.currentPortfolioValue);
        const pnlAmountClass = pnlAmountValid ? (item.pnlAmount > 0 ? 'pnl-positive' : (item.pnlAmount < 0 ? 'pnl-negative' : '')) : '';
        const pnlPercentClass = pnlPercentValid ? (item.pnlPercent > 0 ? 'pnl-positive' : (item.pnlPercent < 0 ? 'pnl-negative' : '')) : '';
        const displayPrice = marketPriceValid ? formatNumber(item.marketPrice, guessDecimalPlaces(item.marketPrice)) : `<span class="error">${item.error || 'لا يوجد سعر'}</span>`;
        const displayAvgPrice = avgPriceValid ? formatNumber(item.averageEntryPrice, guessDecimalPlaces(item.averageEntryPrice)) : (item.totalInvestedAmount > 0 ? '<span class="error">خطأ</span>' : '0.00');
        const displayPortfolioValue = marketPriceValid && portfolioValueValid ? formatNumber(item.currentPortfolioValue, 2) : (item.error ? '-' : '0.00');
        const displayPnlAmount = marketPriceValid && pnlAmountValid ? formatNumber(item.pnlAmount, 2) : (item.error ? '-' : '0.00');
        const displayPnlPercent = marketPriceValid && pnlPercentValid && item.totalInvestedAmount > 0 ? `${formatNumber(item.pnlPercent, 2)}%` : (item.error ? '-' : '0.00%');
        const displayQuantity = !isNaN(item.totalCoinQty) ? formatNumber(item.totalCoinQty, 4) : '<span class="error">خطأ</span>';
        const displayInvested = !isNaN(item.totalInvestedAmount) ? formatNumber(item.totalInvestedAmount, 2) : '<span class="error">خطأ</span>';
        row.innerHTML = `
            <td class="coin-symbol summary-coin-clickable" data-symbol="${item.symbol}" title="تنشيط عملة ${item.symbol}">${item.symbol}</td> <td class="number-col">${displayQuantity}</td>
            <td class="number-col">${displayInvested}</td> <td class="number-col">${displayAvgPrice}</td>
            <td class="number-col">${displayPrice}</td> <td class="number-col">${displayPortfolioValue}</td>
            <td class="number-col ${pnlAmountClass}">${displayPnlAmount}</td> <td class="number-col ${pnlPercentClass}">${displayPnlPercent}</td>
        `;
        summaryTableBody.appendChild(row);
    });
    totalInvestedSummaryEl.textContent = `${formatNumber(totals.invested, 2)} $`;
    totalPnlAmountSummaryEl.textContent = `${formatNumber(totals.pnlAmount, 2)} $`;
    totalCurrentValueSummaryEl.textContent = `${formatNumber(totals.currentValue, 2)} $`;
    totalPnlPercentSummaryEl.textContent = `${formatNumber(totals.pnlPercent, 2)} %`;
    totalPnlAmountSummaryEl.className = totals.pnlAmount > 0 ? 'totals-positive' : (totals.pnlAmount < 0 ? 'totals-negative' : '');
    totalPnlPercentSummaryEl.className = totals.pnlPercent > 0 ? 'totals-positive' : (totals.pnlPercent < 0 ? 'totals-negative' : '');
    totalCurrentValueSummaryEl.className = totals.pnlAmount >= 0 ? 'totals-positive' : 'totals-negative';
}

// --- الدالة الرئيسية لحساب تفاصيل العملة النشطة ---
function calculateActiveCoinDetails() {
    document.getElementById('initialCoinQty').textContent = formatNumber(0, 8);
    document.getElementById('totalCoinQty').textContent = formatNumber(0, 8);
    document.getElementById('totalInvestedAmount').textContent = formatNumber(0, 2);
    document.getElementById('averageEntryPrice').textContent = formatNumber(0, 8);
    document.getElementById('currentPortfolioValue').textContent = formatNumber(0, 2);
    const pnlAmountElement = document.getElementById('pnlAmount');
    const pnlPercentElement = document.getElementById('pnlPercent');
    pnlAmountElement.textContent = formatNumber(0, 2); pnlPercentElement.textContent = formatNumber(0, 2) + '%';
    pnlAmountElement.className = 'value pnl-neutral'; pnlPercentElement.className = 'value pnl-neutral';
    document.getElementById('tpPrice1').textContent = formatNumber(0, 8);
    document.getElementById('tpPrice2').textContent = formatNumber(0, 8);
    document.getElementById('tpPrice3').textContent = formatNumber(0, 8);
    document.getElementById('slPrice').textContent = formatNumber(0, 8);

    for (let i = 1; i <= maxRepurchaseEntries; i++) {
         const dpSpan = document.getElementById(`downPercent${i}`);
         const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
         const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
         const rpPnlPercentDiv = document.getElementById(`repurchasePnlPercent${i}`);
         if (dpSpan) { dpSpan.textContent = '-%'; dpSpan.className = 'down-percent'; }
         if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(0, 8);
         if (rpPnlDiv) { rpPnlDiv.textContent = formatNumber(0, 2); rpPnlDiv.className = 'output-field repurchase-pnl'; }
         if (rpPnlPercentDiv) { rpPnlPercentDiv.textContent = formatNumber(0, 2) + '%'; rpPnlPercentDiv.className = 'output-field repurchase-pnl-percent'; }
    }
    if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;

    const data = allCoinData[activeCoinSymbol];
    const marketPrice = currentMarketPrices[activeCoinSymbol] || 0;
    const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
    const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;
    const initialCoinQty = initialEntryPrice > 0 ? initialAmountDollars / initialEntryPrice : 0;
    document.getElementById('initialCoinQty').textContent = formatNumber(initialCoinQty, 8);
    let totalCoinQty = initialCoinQty; let totalInvestedAmount = initialAmountDollars;

    for (let i = 1; i <= maxRepurchaseEntries; i++) {
        const rpPriceInput = document.getElementById(`repurchasePrice${i}`);
        const rpAmountInput = document.getElementById(`repurchaseAmount${i}`);
        const repurchasePrice = parseFloat(rpPriceInput?.value) || 0;
        const repurchaseAmount = parseFloat(rpAmountInput?.value) || 0;
        let changePercent = 0; let repurchaseQty = 0;
        let pnlForThisRepurchase = 0; let pnlPercentForThisRepurchase = 0;

        if (repurchasePrice > 0 && repurchaseAmount > 0) {
            if (initialEntryPrice > 0) {
                changePercent = ((repurchasePrice - initialEntryPrice) / initialEntryPrice) * 100;
            }
            repurchaseQty = repurchaseAmount / repurchasePrice;
            totalCoinQty += repurchaseQty; totalInvestedAmount += repurchaseAmount;
            if (marketPrice > 0) {
                 const currentValueOfRepurchase = repurchaseQty * marketPrice;
                 pnlForThisRepurchase = currentValueOfRepurchase - repurchaseAmount;
                 pnlPercentForThisRepurchase = repurchaseAmount > 0 ? (pnlForThisRepurchase / repurchaseAmount) * 100 : 0;
             }
        }
        const dpSpan = document.getElementById(`downPercent${i}`);
        const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
        const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
        const rpPnlPercentDiv = document.getElementById(`repurchasePnlPercent${i}`);

        if (dpSpan) {
            dpSpan.textContent = (changePercent !== 0 && isFinite(changePercent)) ? `${formatNumber(changePercent, 2)}%` : '-%';
            dpSpan.className = 'down-percent';
            if (changePercent < 0) dpSpan.classList.add('negative'); else if (changePercent > 0) dpSpan.classList.add('positive');
        }
        if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(repurchaseQty, 8);
        if (rpPnlDiv) {
             rpPnlDiv.textContent = formatNumber(pnlForThisRepurchase, 2);
             rpPnlDiv.className = 'output-field repurchase-pnl';
             if (pnlForThisRepurchase > 0) rpPnlDiv.classList.add('pnl-positive');
             else if (pnlForThisRepurchase < 0) rpPnlDiv.classList.add('pnl-negative');
        }
        if (rpPnlPercentDiv) {
             rpPnlPercentDiv.textContent = `${formatNumber(pnlPercentForThisRepurchase, 2)}%`;
             rpPnlPercentDiv.className = 'output-field repurchase-pnl-percent';
             if (pnlPercentForThisRepurchase > 0) rpPnlPercentDiv.classList.add('pnl-positive');
             else if (pnlPercentForThisRepurchase < 0) rpPnlPercentDiv.classList.add('pnl-negative');
        }
    }

    const averageEntryPrice = totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
    const currentPortfolioValue = totalCoinQty * marketPrice;
    const pnlAmount = (totalInvestedAmount > 0 || currentPortfolioValue > 0 || totalCoinQty > 0) ? currentPortfolioValue - totalInvestedAmount : 0;
    const pnlPercent = totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;
    document.getElementById('totalCoinQty').textContent = formatNumber(totalCoinQty, 8);
    document.getElementById('totalInvestedAmount').textContent = formatNumber(totalInvestedAmount, 2);
    document.getElementById('averageEntryPrice').textContent = formatNumber(averageEntryPrice, guessDecimalPlaces(averageEntryPrice));
    document.getElementById('currentPortfolioValue').textContent = formatNumber(currentPortfolioValue, 2);
    pnlAmountElement.textContent = formatNumber(pnlAmount, 2);
    pnlPercentElement.textContent = `${formatNumber(pnlPercent, 2)}%`;
    pnlAmountElement.className = 'value'; pnlPercentElement.className = 'value';
    if (pnlAmount > 0) { pnlAmountElement.classList.add('pnl-positive'); pnlPercentElement.classList.add('pnl-positive'); }
    else if (pnlAmount < 0) { pnlAmountElement.classList.add('pnl-negative'); pnlPercentElement.classList.add('pnl-negative'); }
    else { pnlAmountElement.classList.add('pnl-neutral'); pnlPercentElement.classList.add('pnl-neutral');}

    const tpP1 = parseFloat(document.getElementById('tpPercent1').value) || 0, tpP2 = parseFloat(document.getElementById('tpPercent2').value) || 0;
    const tpP3 = parseFloat(document.getElementById('tpPercent3').value) || 0, slP = parseFloat(document.getElementById('slPercent').value) || 0;
    const avgPriceDecimals = guessDecimalPlaces(averageEntryPrice);
    const tpPrice1 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP1 / 100) : 0;
    const tpPrice2 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP2 / 100) : 0;
    const tpPrice3 = averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP3 / 100) : 0;
    const slPriceVal = averageEntryPrice > 0 && slP > 0 ? averageEntryPrice * (1 - slP / 100) : 0;
    document.getElementById('tpPrice1').textContent = formatNumber(tpPrice1, avgPriceDecimals);
    document.getElementById('tpPrice2').textContent = formatNumber(tpPrice2, avgPriceDecimals);
    document.getElementById('tpPrice3').textContent = formatNumber(tpPrice3, avgPriceDecimals);
    document.getElementById('slPrice').textContent = formatNumber(slPriceVal, avgPriceDecimals);
}

// --- استدعاء الدوال عند تحميل الصفحة ---
document.addEventListener('DOMContentLoaded', () => {
    loadThemePreference(); createRepurchaseRows(); loadAllDataFromLocalStorage(); updateCoinSelector();
    fetchAllPrices().then(() => {
        if (activeCoinSymbol) displayCoinData(activeCoinSymbol);
        else if (coinSelector.value && allCoinData[coinSelector.value]) { // if a coin is selected in dropdown by updateCoinSelector
            handleCoinSelectionChange();
        }
        else clearUIFields(); // Default if no active or selected coin
        if (autoRefreshCheckbox.checked) startAutoRefresh();
    });

    const inputsToSave = ['initialEntryPrice', 'initialAmountDollars', 'tpPercent1', 'tpPercent2', 'tpPercent3', 'slPercent'];
    inputsToSave.forEach(id => {
        const el = document.getElementById(id); if(el) el.addEventListener('input', saveAndCalculate);
    });
    repurchaseTableBody.addEventListener('input', (event) => {
        if (event.target && (event.target.id.startsWith('repurchasePrice') || event.target.id.startsWith('repurchaseAmount'))) {
            saveAndCalculate();
        }
    });
    autoRefreshCheckbox.addEventListener('change', handleAutoRefreshToggle);
    themeToggleBtn.addEventListener('click', toggleTheme);
    newCoinNameInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') { e.preventDefault(); addOrSwitchCoin(); } });
    coinSelector.addEventListener('change', () => { if (coinSelector.value) newCoinNameInput.value = ''; }); // Clear newCoinName input when a coin is selected from dropdown

    summaryTableBody.addEventListener('click', (event) => {
        let targetCell = event.target;
        // Traverse up to find the TD if a child element was clicked
        while (targetCell && targetCell.tagName !== 'TD' && targetCell !== summaryTableBody) {
            targetCell = targetCell.parentElement;
        }

        if (targetCell && targetCell.classList.contains('summary-coin-clickable')) {
            const symbolToActivate = targetCell.dataset.symbol;
            if (symbolToActivate && allCoinData[symbolToActivate]) {
                coinSelector.value = symbolToActivate; // Update dropdown
                handleCoinSelectionChange(); // Process selection change
                // Scroll to controls bar for better UX
                const controlsBar = document.querySelector('.controls-bar');
                if (controlsBar) controlsBar.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }
    });
});
