/* ===== متغيرات CSS الرئيسية ===== */
:root {
    /* Updated Light Theme Colors */
    --bg-color: #f4f6f9;
    --section-bg: #ffffff;
    --text-color: #212529;
    --text-muted: #6c757d;
    --text-light: #f8f9fa; /* Used for text on dark backgrounds like page-header */
    --primary-color: #0056b3;
    --secondary-color: #007bff;
    --border-color: #e0e0e0;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --hover-bg: #e9ecef;
    --table-header-bg: #f8f9fa;
    --dark-header-bg: #343a40; /* Page top header */
    --positive-color: #198754;
    --negative-color: #dc3545;
    --link-color: #0d6efd;
    --link-hover-color: #0a58ca;
    --header-link-color: #adb5bd;
    --header-link-hover-color: #f8f9fa;
    --shadow-color: rgba(0,0,0,0.05);
    --output-field-bg: #eef1f5;
    --output-field-text: #495057;
    --sl-output-bg: #f8d7da;
    --sl-output-text: #721c24;
    --sl-output-border: #f1c0c5;
    --telegram-link-color: #4da6ff;
    --focus-ring-color: rgba(0, 123, 255, 0.25);
    --font-weight-bold: 700;
    --font-weight-semibold: 600;
    --font-weight-normal: 500;
}

.dark-mode {
    /* Original Dark Theme Overrides */
    --bg-color: #121212;
    --section-bg: #1e1e1e;
    --text-color: #e0e0e0;
    --text-muted: #888;
    --text-light: #f2f2f2;
    --primary-color: #4dabf7;
    --secondary-color: #74c0fc;
    --border-color: #333;
    --input-bg: #2a2a2a;
    --input-border: #444;
    --hover-bg: #333;
    --table-header-bg: #2a2a2a;
    --dark-header-bg: #212529;
    --positive-color: #20c997;
    --negative-color: #fa5252;
    --link-color: #4dabf7;
    --link-hover-color: #74c0fc;
    --header-link-color: #9ab;
    --header-link-hover-color: #cde;
    --telegram-link-color: #6699cc;
    --shadow-color: rgba(255,255,255,0.08);
    --output-field-bg: #2c2c2c;
    --output-field-text: #ccc;
    --sl-output-bg: #5c2a30;
    --sl-output-text: #ffc0c7;
    --sl-output-border: #8a3f47;
    --focus-ring-color: rgba(77, 171, 247, 0.3);
}

/* ===== الأنماط الأساسية ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6; 
    margin: 0; 
    font-size: 14px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.page-header {
    text-align: center; 
    padding: 15px 20px;
    background-color: var(--dark-header-bg); 
    color: var(--text-light);
    font-size: 1em; 
    border-bottom: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.page-header strong { 
    font-weight: var(--font-weight-semibold); 
}

.page-header a {
    color: var(--header-link-color); 
    text-decoration: none;
    font-weight: var(--font-weight-normal); 
    margin: 0 5px;
    transition: color 0.2s ease;
}

.page-header a:hover { 
    text-decoration: underline; 
    color: var(--header-link-hover-color); 
}

.page-header a.telegram-link {
    color: var(--telegram-link-color) !important;
    font-weight: var(--font-weight-bold) !important;
}

.page-header a.telegram-link:hover { 
    text-decoration: underline; 
    filter: brightness(1.1); 
}

.container { 
    max-width: 1920px; 
    margin: 20px auto; 
    padding: 0 25px; 
}

h1 { 
    text-align: center; 
    color: var(--primary-color); 
    margin-top: 15px; 
    margin-bottom: 30px; 
    font-weight: var(--font-weight-bold); 
    font-size: 1.8em; 
    transition: color 0.3s ease; 
}

h2 { 
    margin-top: 0; 
    color: var(--primary-color); 
    font-size: 1.4em; 
    border-bottom: 2px solid var(--secondary-color); 
    padding-bottom: 10px; 
    margin-bottom: 25px; 
    font-weight: var(--font-weight-semibold); 
    transition: color 0.3s ease, border-color 0.3s ease; 
}

h3 { 
    font-size: 1.2em; 
    color: var(--primary-color); 
    margin-bottom: 15px; 
    margin-top: 0; 
    font-weight: var(--font-weight-semibold); 
    transition: color 0.3s ease;
}

/* ===== قسم الملخص العام ===== */
.overall-summary-section {
    background-color: var(--section-bg); 
    padding: 20px 25px; 
    margin-bottom: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 12px var(--shadow-color);
    position: sticky; 
    top: 0; 
    z-index: 1000; 
    border: 1px solid var(--border-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.summary-table-container {
    max-height: 280px; 
    overflow-y: auto; 
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 20px;
    background-color: var(--bg-color);
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.summary-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.9em;
}

.summary-table th, .summary-table td {
    border-bottom: 1px solid var(--border-color);
    padding: 10px 14px;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    transition: border-color 0.3s ease, background-color 0.2s ease;
}

.summary-table th {
    background-color: var(--table-header-bg); 
    color: var(--text-color); 
    position: sticky; 
    top: 0; 
    z-index: 1;
    font-weight: var(--font-weight-semibold);
    border-top: 1px solid var(--border-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* حدود وزوايا الجدول */
.summary-table td:first-child, .summary-table th:first-child { 
    border-right: 1px solid var(--border-color); 
    border-top-right-radius: 6px; 
}

.summary-table td:last-child, .summary-table th:last-child { 
    border-left: 1px solid var(--border-color); 
    border-top-left-radius: 6px;
}

.summary-table tr:last-child td { 
    border-bottom: none; 
}

.summary-table tr:last-child td:first-child { 
    border-bottom-right-radius: 6px; 
}

.summary-table tr:last-child td:last-child { 
    border-bottom-left-radius: 6px; 
}

.summary-table tr:nth-child(even) { 
    background-color: transparent; 
}

.summary-table tr:hover td { 
    background-color: var(--hover-bg); 
}

.summary-table tbody td { 
    font-weight: var(--font-weight-normal); 
}

.summary-table .coin-symbol { 
    text-align: right; 
    padding-right: 15px; 
    font-weight: var(--font-weight-semibold); 
}

.summary-table .number-col { 
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace; 
    direction: ltr; 
    text-align: center; 
    font-weight: var(--font-weight-semibold); 
}

.summary-table .pnl-positive, .totals-positive { 
    color: var(--positive-color) !important; 
    font-weight: var(--font-weight-bold); 
}

.summary-table .pnl-negative, .totals-negative { 
    color: var(--negative-color) !important; 
    font-weight: var(--font-weight-bold); 
}

.summary-table .error { 
    color: var(--negative-color); 
    font-style: italic; 
    font-size: 0.9em; 
    font-weight: var(--font-weight-normal); 
}

.summary-coin-clickable { 
    cursor: pointer; 
    transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out; 
}

.summary-coin-clickable:hover { 
    background-color: var(--hover-bg); 
    color: var(--primary-color); 
    font-weight: var(--font-weight-bold); 
}

/* ===== إجماليات الملخص ===== */
.summary-totals {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    background-color: var(--section-bg);
    padding: 15px 20px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid var(--border-color); 
    font-weight: var(--font-weight-bold); 
    text-align: center;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.summary-totals div { 
    display: flex; 
    flex-direction: column; 
    flex-basis: auto; 
    flex-grow: auto; 
}

.summary-totals label {
    font-size: 0.9em; 
    color: var(--text-muted);
    margin-bottom: 5px;
    font-weight: var(--font-weight-normal); 
    transition: color 0.3s ease;
}

.summary-totals span {
    font-size: 1.3em;
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    font-weight: var(--font-weight-bold);
    transition: color 0.3s ease;
}

/* ===== شريط التحكم ===== */
.controls-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    background-color: var(--section-bg);
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    border: 1px solid var(--border-color);
    align-items: center;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.controls-bar label {
    font-weight: var(--font-weight-semibold);
    margin-left: 5px;
    color: var(--primary-color);
    flex-shrink: 0;
    font-size: 0.95em;
}

.controls-bar select, .controls-bar input[type="text"] {
    padding: 10px 14px;
    border: 1px solid var(--input-border);
    border-radius: 6px;
    font-size: 1em;
    flex-grow: 1;
    min-width: 160px;
    background-color: var(--input-bg);
    color: var(--text-color);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.06);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.controls-bar select:focus, .controls-bar input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--focus-ring-color), inset 0 1px 3px rgba(0,0,0,0.06);
}

.controls-bar button {
    padding: 10px 20px;
    font-size: 0.95em;
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 6px;
    flex-shrink: 0;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

.controls-bar button:hover {
    background-color: var(--primary-color);
    transform: translateY(-1px);
}

.controls-bar button:active {
    transform: translateY(0px);
}

.controls-bar button#deleteCoinBtn {
    background-color: var(--negative-color);
}

.controls-bar button#deleteCoinBtn:hover {
    opacity: 0.85;
    background-color: var(--negative-color);
}

.controls-bar button#refreshPriceBtn {
    background-color: var(--positive-color);
    padding: 8px 12px;
    font-size: 1.2em;
}

.controls-bar button#refreshPriceBtn:hover {
    background-color: #146c43;
}

#apiStatus {
    flex-grow: 1;
    text-align: left;
    font-size: 0.9em;
    color: var(--text-muted);
    margin: 0 10px;
    flex-shrink: 1;
    min-width: 150px;
    transition: color 0.3s ease;
}

#coinStatus {
    font-size: 0.95em;
    color: var(--text-muted);
    flex-shrink: 0;
    transition: color 0.3s ease;
    font-weight: var(--font-weight-normal);
}

#themeToggleBtn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 1.3em;
    padding: 5px 9px;
    line-height: 1;
    margin-left: 10px;
    transition: color 0.3s ease, border-color 0.3s ease, background-color 0.2s ease, transform 0.1s ease;
}

#themeToggleBtn:hover {
    background-color: var(--hover-bg);
    color: var(--text-color);
    transform: translateY(-1px);
}

/* ===== تخطيط المحتوى التفصيلي ===== */
.details-content-wrapper {
    display: grid;
    grid-template-columns: 0.8fr 1.5fr 1fr;
    gap: 25px;
    align-items: flex-start;
}

.section {
    background-color: var(--section-bg);
    padding: 25px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    box-shadow: 0 3px 10px var(--shadow-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.section > *:last-child {
    margin-bottom: 0;
}

/* ===== النماذج والحقول ===== */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px 15px;
    margin-bottom: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

label {
    margin-bottom: 8px;
    font-weight: var(--font-weight-semibold);
    color: var(--text-muted);
    font-size: 0.9em;
    transition: color 0.3s ease;
}

input[type="text"], input[type="number"], select {
    padding: 10px 14px;
    border: 1px solid var(--input-border);
    border-radius: 6px;
    font-size: 1em;
    box-sizing: border-box;
    width: 100%;
    background-color: var(--input-bg);
    color: var(--text-color);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.06);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.2s ease, box-shadow 0.2s ease;
    font-weight: var(--font-weight-normal);
}

input[type="number"] {
    -moz-appearance: textfield;
}

input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="text"]:focus, input[type="number"]:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--focus-ring-color), inset 0 1px 3px rgba(0,0,0,0.06);
}

/* ===== حقول الإخراج ===== */
.output-field, .input-field-read-only {
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--output-field-bg);
    font-weight: var(--font-weight-bold);
    min-height: 42px;
    display: flex;
    align-items: center;
    color: var(--output-field-text);
    font-size: 1em;
    word-break: break-all;
    box-sizing: border-box;
    width: 100%;
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    direction: ltr;
    text-align: left;
}

.output-field.error {
    color: var(--negative-color);
    background-color: rgba(220, 53, 69, 0.05);
    border-color: rgba(220, 53, 69, 0.3);
}

/* ===== مجموعة عرض السعر ===== */
.market-price-display-group {
    grid-column: span 2;
    display: flex;
    align-items: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.market-price-display-group .form-group {
    flex-grow: 1;
    margin-bottom: 0;
}

#marketPriceDisplay {
    font-size: 1.1em;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    background-color: var(--bg-color);
}

.auto-refresh-group {
    grid-column: span 2;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95em;
    margin-top: 10px;
    background-color: var(--output-field-bg);
    padding: 8px 12px;
    border-radius: 6px;
}

.auto-refresh-group input[type="checkbox"] {
    margin-left: 5px;
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.auto-refresh-group label {
    margin-bottom: 0;
    font-weight: var(--font-weight-normal);
    color: var(--text-muted);
}

/* ===== قسم الأهداف ووقف الخسارة ===== */
#targetsSection {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px dashed var(--border-color);
    transition: border-color 0.3s ease;
}

.targets-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 18px;
}

.target-pair {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    align-items: end;
}

.targets-grid label {
    font-size: 0.85em;
    text-align: center;
    margin-bottom: 4px;
    display: block;
    font-weight: var(--font-weight-normal);
}

.targets-grid .form-group {
    margin-bottom: 0;
}

.targets-grid input[type="number"] {
    text-align: center;
    padding: 8px 10px;
}

.targets-grid .output-field {
    font-size: 1em;
    text-align: center;
    padding: 9px 10px;
    min-height: 38px;
    font-weight: var(--font-weight-bold);
}

.sl-group {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;
}

.sl-input input {
    border-color: var(--negative-color);
    background-color: rgba(220, 53, 69, 0.05);
}

.sl-input input:focus {
    border-color: var(--negative-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.sl-output {
    background-color: var(--sl-output-bg) !important;
    color: var(--sl-output-text) !important;
    border-color: var(--sl-output-border) !important;
    font-weight: var(--font-weight-bold);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ===== قسم التعزيز ===== */
.section-repurchase {
    overflow: auto;
}

.repurchase-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 15px;
    font-size: 0.9em;
}

.repurchase-table th, .repurchase-table td {
    border-bottom: 1px solid var(--border-color);
    padding: 8px 6px;
    text-align: center;
    white-space: nowrap;
    transition: border-color 0.3s ease, background-color 0.2s ease;
}

.repurchase-table th {
    background-color: var(--table-header-bg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 1;
    border-top: 1px solid var(--border-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* حدود وزوايا جدول التعزيز */
.repurchase-table td:first-child, .repurchase-table th:first-child {
    border-right: 1px solid var(--border-color);
}

.repurchase-table td:last-child, .repurchase-table th:last-child {
    border-left: 1px solid var(--border-color);
}

.repurchase-table th:first-child {
    border-top-right-radius: 6px;
}

.repurchase-table th:last-child {
    border-top-left-radius: 6px;
}

.repurchase-table tr:last-child td {
    border-bottom: none;
}

.repurchase-table tr:last-child td:first-child {
    border-bottom-right-radius: 6px;
}

.repurchase-table tr:last-child td:last-child {
    border-bottom-left-radius: 6px;
}

.repurchase-table tr:hover td {
    background-color: var(--hover-bg);
}

.repurchase-table input[type="number"] {
    width: 90%;
    padding: 6px 8px;
    font-size: 1em;
    text-align: center;
    margin: 0 auto;
    display: block;
    box-shadow: none;
}

.repurchase-table .output-field {
    font-size: 1em;
    padding: 6px;
    min-height: 34px;
    width: auto;
    background-color: transparent;
    border: none;
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    text-align: center;
}

.repurchase-table .repurchase-pnl, .repurchase-table .repurchase-pnl-percent {
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    font-weight: var(--font-weight-bold);
    direction: ltr;
    text-align: center;
}

.down-percent {
    font-weight: var(--font-weight-semibold);
    min-width: 60px;
    display: inline-block;
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    direction: ltr;
    padding: 3px 5px;
    border-radius: 4px;
    font-size: 0.9em;
}

.down-percent.positive {
    color: var(--positive-color);
    background-color: rgba(25, 135, 84, 0.1);
}

.down-percent.negative {
    color: var(--negative-color);
    background-color: rgba(220, 53, 69, 0.1);
}

/* ===== شبكة الملخص ===== */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 15px;
}

.summary-item {
    background-color: var(--bg-color);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: background-color 0.3s ease, border-color 0.3s ease;
    display: flex;
    flex-direction: column;
}

.summary-item label {
    font-size: 0.85em;
    color: var(--text-muted);
    display: block;
    margin-bottom: 6px;
    font-weight: var(--font-weight-normal);
    transition: color 0.3s ease;
}

.summary-item .value {
    font-size: 1.2em;
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    word-wrap: break-word;
    margin-top: auto;
    font-family: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
    transition: color 0.3s ease;
    direction: ltr;
    text-align: left;
}

/* ===== فئات الربح والخسارة ===== */
.pnl-positive {
    color: var(--positive-color) !important;
}

.pnl-negative {
    color: var(--negative-color) !important;
}

.pnl-neutral {
    color: var(--text-color) !important;
}

/* ===== استعلامات الوسائط للحاسوب ===== */
@media (max-width: 1200px) {
    .details-content-wrapper {
        grid-template-columns: 1fr 1fr;
    }

    .details-content-wrapper > .section-current-targets:nth-child(3) {
        grid-column: 1 / -1;
    }
}

@media (max-width: 992px) {
    .summary-table {
        font-size: 0.85em;
    }

    .summary-table th, .summary-table td {
        padding: 8px 10px;
    }

    .summary-totals {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }

    .details-content-wrapper {
        grid-template-columns: 1fr;
    }
}
